# Ingestion script for RAG QA system

from utils.detect_file_type import detect_file_type
from loaders.pdf_loader import load_pdf
from loaders.docx_loader import load_docx
from loaders.excel_loader import load_excel
from loaders.csv_loader import load_csv
from loaders.sqlite_loader import load_sqlite
from loaders.text_loader import load_text

LOADER_MAP = {
    'pdf': load_pdf,
    'docx': load_docx,
    'excel': load_excel,
    'csv': load_csv,
    'sqlite': load_sqlite,
    'text': load_text
}

def load_file(file_path):
    file_type = detect_file_type(file_path)
    loader = LOADER_MAP.get(file_type)
    if loader is None:
        raise ValueError(f"Unsupported file type: {file_type}")
    return loader(file_path)
