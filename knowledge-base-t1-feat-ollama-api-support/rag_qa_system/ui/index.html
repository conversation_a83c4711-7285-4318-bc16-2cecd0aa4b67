<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>PayPhi Genie</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body {
            background: #f6f8fa;
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        .layout {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: center;
            width: 100vw;
            min-height: 100vh;
        }
        .sidebar {
            position: sticky;
            top: 0;
            align-self: flex-start;
            width: 260px;
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.08);
            margin: 40px 24px 0 0;
            padding: 24px 16px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }
        .sidebar h2 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 16px;
        }
        .chat-list {
            flex: 1;
            margin-bottom: 16px;
        }
        .chat-list-item {
            padding: 8px 0;
            cursor: pointer;
            color: #2563eb;
            border-bottom: 1px solid #f1f5f9;
        }
        .chat-list-item:hover {
            background: #f1f5f9;
        }
        .new-chat-btn {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 10px 18px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            margin-top: 8px;
        }
        .container {
            background: #fff;
            margin-top: 40px;
            padding: 32px 24px;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.08);
            width: 90%;
            max-width: 900px;
        }
        h1 {
            font-size: 2rem;
            font-weight: 600;
            color: #222;
            margin-bottom: 24px;
            text-align: center;
        }
        form {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }
        input[type="text"] {
            flex: 1;
            padding: 12px;
            border: 1px solid #e1e4e8;
            border-radius: 8px;
            font-size: 1rem;
        }
        button {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        button:hover {
            background: #1e40af;
        }
        #answer { display: none; }
        .chatgpt-chat-area {
            flex: 1;
            overflow-y: auto;
            padding: 24px 24px 0 24px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        .chatgpt-message {
            max-width: 70%;
            padding: 16px 20px;
            border-radius: 16px;
            font-size: 1.08rem;
            line-height: 1.6;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            margin-bottom: 4px;
            word-break: break-word;
        }
        .chatgpt-message.user {
            align-self: flex-end;
            background: #2563eb;
            color: #fff;
            border-bottom-right-radius: 4px;
        }
        .chatgpt-message.assistant {
            align-self: flex-start;
            background: #f1f5f9;
            color: #222;
            border-bottom-left-radius: 4px;
        }
        .refs {
            margin-top: 10px;
            font-size: 0.98rem;
            color: #555;
        }
        .chatgpt-input-area {
            display: flex;
            gap: 12px;
            padding: 16px 24px 24px 24px;
            background: #fff;
            border-bottom-left-radius: 16px;
            border-bottom-right-radius: 16px;
        }
        .footer {
            position: fixed;
            right: 32px;
            bottom: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.95);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            padding: 8px 16px;
            z-index: 100;
            font-size: 1rem;
        }
        .footer-logo {
            height: 28px;
            width: auto;
        }
        
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <h2>Chats</h2>
            <div class="chat-list" id="chat-list"></div>
            <button class="new-chat-btn" id="newChatBtn">New Chat</button>
        </div>
        <div class="container">
            <div style="display:flex;align-items:center;justify-content:space-between;">
                <h1 style="margin-bottom:0;">PayPhi Genie</h1>
                <div id="llm-info" style="display:flex;align-items:center;gap:6px;font-size:1.05rem;color:#2563eb;cursor:pointer;" title="LLM backend and model info">
                    <span id="llm-info-icon" style="font-size:1.3em;">&#9432;</span>
                    <span id="llm-info-text">LLM: ...</span>
                </div>
            </div>
            <div class="chatgpt-chat-area" id="chat-log"></div>
            <form id="askForm" class="chatgpt-input-area" autocomplete="off">
                <input type="text" name="q" id="questionInput" placeholder="Ask a question..." required autofocus onkeydown="if(event.key==='Enter'){event.preventDefault();document.getElementById('askBtn').click();}" />
                <button type="submit" id="askBtn">Ask</button>
            </form>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        let allChats = [];
        let chatLog = [];
        let currentChatIdx = null;

        let liveTimerInterval = null;
        function renderChatLog() {
            const logDiv = document.getElementById('chat-log');
            // Top-down order (oldest at top)
            // Render chat bubbles as empty divs, then fill with HTML for markdown
            logDiv.innerHTML = chatLog.map((entry, i) => {
                let refs = '';
                let timer = '';
                if (entry.refs && entry.refs.length > 0) {
                    refs = `<div class=\"refs\"><b>References:</b><ul>` +
                        entry.refs.map(r => {
                            if (typeof r === 'object' && r.doc) {
                                return `<li><b>${r.doc}</b>${r.page !== undefined ? ` (Page: ${r.page})` : ''}</li>`;
                            } else {
                                return `<li>${r}</li>`;
                            }
                        }).join('') +
                        `</ul></div>`;
                }
                let copyBtn = '';
                if (entry.role === 'assistant') {
                    if (entry.responseTime) {
                        timer = `<div style=\"font-size:0.85em;color:#888;margin-top:4px;\">⏱️ ${entry.responseTime} ms</div>`;
                    }
                    copyBtn = `<button class=\"copy-btn\" data-idx=\"${i}\" style=\"margin-top:8px;display:block;font-size:0.95em;padding:4px 12px;border-radius:6px;border:1px solid #e1e4e8;background:#f6f8fa;cursor:pointer;float:right;\">Copy</button>`;
                }
                if (entry.role === 'user' && entry.showLiveTimer) {
                    timer = `<div id=\"live-timer\" style=\"font-size:0.85em;color:#888;margin-top:4px;\">⏱️ <span id=\"live-timer-val\">0</span> ms</div>`;
                }
                // Use a placeholder for markdown content, to be filled after
                if (entry.role === 'user') {
                    return `<div class=\"chatgpt-message user\" id=\"msg-${i}\">${timer}</div>`;
                } else {
                    return `<div class=\"chatgpt-message assistant\" id=\"msg-${i}\">${refs}${timer}${copyBtn}</div>`;
                }
            }).join('');
            // Now fill in markdown content as HTML
            chatLog.forEach((entry, i) => {
                const el = document.getElementById(`msg-${i}`);
                if (el) {
                    if (entry.role === 'user') {
                        el.insertAdjacentHTML('afterbegin', entry.a);
                    } else {
                        el.insertAdjacentHTML('afterbegin', entry.a);
                    }
                }
            });
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Copy button event delegation
        document.getElementById('chat-log').addEventListener('click', function(e) {
            if (e.target && e.target.classList.contains('copy-btn')) {
                const idx = parseInt(e.target.getAttribute('data-idx'));
                const entry = chatLog[idx];
                // Remove HTML tags for copying plain text, or copy as markdown if desired
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = entry.a;
                const text = tempDiv.innerText;
                navigator.clipboard.writeText(text);
                e.target.innerText = 'Copied!';
                setTimeout(() => { e.target.innerText = 'Copy'; }, 1200);
            }
        });

        function renderChatList() {
            const listDiv = document.getElementById('chat-list');
            if (allChats.length === 0) {
                listDiv.innerHTML = '<div style="color:#888;">No chats yet</div>';
                return;
            }
            listDiv.innerHTML = allChats.map((chat, idx) => {
                const firstQ = chat[0]?.q || 'Chat ' + (idx+1);
                return `<div class="chat-list-item" onclick="selectChat(${idx})">${firstQ}</div>`;
            }).join('');
        }

        function selectChat(idx) {
            chatLog = allChats[idx];
            currentChatIdx = idx;
            renderChatLog();
        }
        window.selectChat = selectChat;

        document.getElementById('askForm').onsubmit = async function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const question = formData.get('q');
            // Add user message with live timer
            chatLog.push({ role: 'user', a: marked.parseInline(question), showLiveTimer: true });
            renderChatLog();
            let ms = 0;
            const liveTimer = setInterval(() => {
                ms += 50;
                const el = document.getElementById('live-timer-val');
                if (el) el.innerText = ms;
            }, 50);
            liveTimerInterval = liveTimer;
            const start = performance.now();
            const res = await fetch('http://127.0.0.1:8000/ask?q=' + encodeURIComponent(question) + '&format=markdown');
            const data = await res.json();
            const end = performance.now();
            clearInterval(liveTimer);
            liveTimerInterval = null;
            // Remove live timer from last user message
            if (chatLog.length > 0 && chatLog[chatLog.length-1].role === 'user') {
                delete chatLog[chatLog.length-1].showLiveTimer;
            }
            const responseTime = Math.round(end - start);
            // Clean LLM response: remove triple backticks and language hints
            let answerText = data.answer.trim();
            if (answerText.startsWith('```')) {
                answerText = answerText.replace(/^```[a-zA-Z]*\n?/, '').replace(/```$/, '').trim();
            }
            // Detect LLM backend from response (API or subprocess)
            let llmMethod = data.llm_backend || 'Unknown';
            let llmModel = data.llm_model || 'Unknown';
            document.getElementById('llm-info-text').innerText = 'LLM: ' + llmMethod;
            document.getElementById('llm-info').title = `LLM backend: ${llmMethod}\nModel: ${llmModel}`;
            chatLog.push({ role: 'assistant', a: marked.parse(answerText), refs: data.chunks, responseTime });
            renderChatLog();
            // Save to allChats
            if (currentChatIdx === null) {
                allChats.push([...chatLog]);
                currentChatIdx = allChats.length - 1;
            } else {
                allChats[currentChatIdx] = [...chatLog];
            }
            renderChatList();
        };

        document.getElementById('newChatBtn').onclick = function() {
            chatLog = [];
            currentChatIdx = null;
            renderChatLog();
            // Center the input box for new chat
            document.getElementById('chat-log-container').style.justifyContent = 'center';
            document.getElementById('askForm').style.marginTop = 'auto';
        };

        // On first load, center input if no chat
        if (chatLog.length === 0) {
            document.getElementById('chat-log-container').style.justifyContent = 'center';
            document.getElementById('askForm').style.marginTop = 'auto';
        }

        // When a message is sent, move input to bottom
        function moveInputToBottom() {
            document.getElementById('chat-log-container').style.justifyContent = 'flex-end';
            document.getElementById('askForm').style.marginTop = '16px';
        }

        // Patch askForm submit to move input to bottom after first message
        const origSubmit = document.getElementById('askForm').onsubmit;
        document.getElementById('askForm').onsubmit = function(e) {
            origSubmit.call(this, e);
            moveInputToBottom();
        };

        // Initial render
        renderChatList();
        renderChatLog();
    </script>
    <footer class="footer">
        <span>Powered by Prysom Systems PVT LTD</span>
    </footer>
</body>
</html>
