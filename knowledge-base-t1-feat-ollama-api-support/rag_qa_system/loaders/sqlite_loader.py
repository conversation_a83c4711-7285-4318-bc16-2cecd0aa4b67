def load_sqlite(file_path):
    import sqlite3
    conn = sqlite3.connect(file_path)
    cursor = conn.cursor()
    # Get all table names
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [row[0] for row in cursor.fetchall()]
    text_chunks = []
    for table in tables:
        text_chunks.append(f"Table: {table}\n")
        cursor.execute(f"SELECT * FROM {table}")
        rows = cursor.fetchall()
        col_names = [desc[0] for desc in cursor.description]
        text_chunks.append("\t".join(col_names))
        for row in rows:
            text_chunks.append("\t".join(str(cell) for cell in row))
        text_chunks.append("")
    conn.close()
    return "\n".join(text_chunks)
