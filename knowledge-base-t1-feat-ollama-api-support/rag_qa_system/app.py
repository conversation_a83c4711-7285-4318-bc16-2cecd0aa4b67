# Entry point for the RAG QA system
import argparse
from ingest import load_file

def main():
    parser = argparse.ArgumentParser(description="PayPhi Konwledge Base Genie - File Ingestion Demo")
    parser.add_argument("file", type=str, help="Path to the file to ingest")
    args = parser.parse_args()

    file_path = args.file
    try:
        content = load_file(file_path)
        print("Loaded content (truncated to 500 chars):\n")
        print(str(content)[:500])
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
