# FAISS vector store logic
import faiss
import numpy as np
import pickle

class VectorStore:
    def __init__(self, dim, index_path, meta_path):
        self.index = faiss.IndexFlatL2(dim)
        self.index_path = index_path
        self.meta_path = meta_path
        self.chunks = []

    def add(self, embeddings, chunks):
        self.index.add(np.array(embeddings).astype('float32'))
        self.chunks.extend(chunks)

    def save(self):
        faiss.write_index(self.index, self.index_path)
        with open(self.meta_path, 'wb') as f:
            pickle.dump(self.chunks, f)

    def load(self):
        self.index = faiss.read_index(self.index_path)
        with open(self.meta_path, 'rb') as f:
            self.chunks = pickle.load(f)

    def search(self, query_emb, top_k=5):
        D, I = self.index.search(np.array([query_emb]).astype('float32'), top_k)
        return [(self.chunks[i], D[0][idx]) for idx, i in enumerate(I[0])]
