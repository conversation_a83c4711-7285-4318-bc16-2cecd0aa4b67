# FastAPI backend for file upload and Q&A

from fastapi import FastAP<PERSON>, HTTPException, Query
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import os


app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8080", "http://127.0.0.1:8080", "http://localhost", "http://127.0.0.1"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Welcome to the PayPhi Genie API!"}



def get_documents():
    docs_path = os.path.join(os.path.dirname(__file__), '..', 'documents')
    files = [os.path.join(docs_path, f) for f in os.listdir(docs_path) if os.path.isfile(os.path.join(docs_path, f))]
    return files



@app.get("/ask")
async def ask_question(q: str):
    from app.ingestion import ingest_embed_store, retrieve_and_answer
    from app.vector_store import VectorStore
    from app.llm_runner import BASE_PROMPT, generate_answer, rewrite_prompt_with_meta
    files = get_documents()
    if not files:
        raise HTTPException(status_code=404, detail="No documents found.")
    # Assume all files are ingested into a single vector store
    dim = 384  # all-MiniLM-L6-v2 embedding size
    index_path = os.path.join(os.path.dirname(__file__), '..', 'vector.index')
    meta_path = os.path.join(os.path.dirname(__file__), '..', 'vector.meta')
    vector_store = VectorStore(dim, index_path, meta_path)
    # Ingest and embed all files if index does not exist
    if not os.path.exists(index_path) or not os.path.exists(meta_path):
        for file_path in files:
            ingest_embed_store(file_path, vector_store)
    else:
        vector_store.load()
    answer, chunks = retrieve_and_answer(q, vector_store)
    # Use meta-prompt to rewrite the base prompt based on user query
    context_str = "\n\n".join([c for c, _ in chunks])
    model = "phi"
    rewritten_prompt = rewrite_prompt_with_meta(BASE_PROMPT.format(context=context_str, question=q), q, model)
    answer_md, llm_backend, used_model = generate_answer(rewritten_prompt, model)
    # Try to extract doc name and page from chunk metadata if available
    refs = []
    for _, meta in chunks:
        doc = meta.get('doc') if isinstance(meta, dict) else None
        page = meta.get('page') if isinstance(meta, dict) else None
        if doc and page:
            refs.append({'doc': doc, 'page': page})
        else:
            refs.append(str(meta))
    return JSONResponse({
        "answer": answer_md,
        "chunks": refs,
        "llm_backend": llm_backend,
        "llm_model": used_model
    })

# Ollama status endpoint
@app.get("/ollama_status")
async def ollama_status():
    import subprocess
    try:
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            return {"connected": True, "models": result.stdout.strip().splitlines()}
        else:
            return {"connected": False, "error": result.stderr.strip()}
    except Exception as e:
        return {"connected": False, "error": str(e)}
