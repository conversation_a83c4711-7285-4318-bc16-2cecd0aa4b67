# Interface with local LLM (Ollama or llama.cpp)
import subprocess

import requests

def generate_answer(prompt, model="phi"):
    """
    Try Ollama API first, fallback to subprocess if API fails. Returns (answer, backend, model).
    """
    # Try Ollama API (http://localhost:11434/api/generate)
    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={"model": model, "prompt": prompt, "stream": False},
            timeout=20
        )
        if response.ok:
            data = response.json()
            if 'response' in data:
                return data['response'].strip(), 'Ollama API', model
    except Exception:
        pass
    # Fallback to subprocess
    result = subprocess.run(["ollama", "run", model, prompt], capture_output=True, text=True)
    return result.stdout.strip(), 'Ollama Subprocess', model


# Meta-prompt template for dynamic prompt rewriting
META_PROMPT_TEMPLATE = '''
You are an advanced language assistant.
Your task is to **rewrite the user's original prompt** based on:

1. The **writing style implied or requested** in the user's query (e.g., Wikipedia article, research paper, email, blog post).
2. The **intent** of the original prompt (e.g., generating context-based answers, summarizing documents, extracting key facts).

### Instructions:
* Maintain the **core functional goals** of the original prompt.
* Adapt the **tone, structure, and formatting** to match the style suggested by the user query.
* Use headings, bullet points, and other formatting relevant to the chosen style.
* If the writing style is not explicitly stated, **infer the most appropriate one** from the query.

---

### Inputs:
* **User's Original Prompt**:
  {original_prompt}

* **User's Query or Style Request**:
  {user_query}

---

### Output:
A **rewritten version** of the prompt that:
* Preserves the original intent
* Adopts the writing style implied or requested by the user
'''

# Default prompt for context-based answering
BASE_PROMPT = '''
You are a helpful assistant. Using ONLY the provided context, answer the user's question below in a visually rich, well-structured Markdown format. Use lists, tables, bold, italics, and other Markdown features to make the answer easy to read. At the end, include a section called "References" listing the relevant document chunks or filenames you used. If the answer is not in the context, reply: "I cannot answer this question based on the provided documents."

Context:
{context}

Question: {question}

Answer (in Markdown):
'''

def rewrite_prompt_with_meta(original_prompt, user_query, model="phi"):
    """
    Uses the meta-prompt to rewrite the original prompt based on the user's query/style request.
    """
    meta_prompt = META_PROMPT_TEMPLATE.format(
        original_prompt=original_prompt.strip(),
        user_query=user_query.strip()
    )
    # Call LLM to rewrite the prompt
    rewritten, _, _ = generate_answer(meta_prompt, model)
    return rewritten.strip()
