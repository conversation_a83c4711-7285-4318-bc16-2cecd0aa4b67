def retrieve_and_answer(query, vector_store, top_k=5):
    """
    Retrieves top-k relevant chunks for a query and generates an answer using the LLM.
    Returns the answer and the retrieved chunks.
    """
    from app.retriever import retrieve
    from app.llm_runner import generate_answer
    results = retrieve(query, vector_store, top_k=top_k)
    context = "\n".join([chunk for chunk, _ in results])
    prompt = f"Context:\n{context}\n\nQuestion: {query}\nAnswer:"
    answer = generate_answer(prompt)
    return answer, results
def ingest_and_chunk(file_path, chunk_size=500, chunk_overlap=50):
    """
    Loads a file, chunks its text, and returns the chunks.
    """
    text = load_file(file_path)
    chunks = chunk_text(text, chunk_size=chunk_size, chunk_overlap=chunk_overlap)
    return chunks


def ingest_embed_store(file_path, vector_store, chunk_size=500, chunk_overlap=50):
    """
    Loads, chunks, embeds, and stores the file's text in the given vector store.
    Returns the chunk texts and their embeddings.
    """
    from app.embedder import embed_texts
    chunks = ingest_and_chunk(file_path, chunk_size, chunk_overlap)
    embeddings = embed_texts(chunks)
    vector_store.add(embeddings, chunks)
    vector_store.save()
    return chunks, embeddings
# Handles file ingestion and text extraction
from pathlib import Path
from loaders.pdf_loader import load_pdf
from loaders.docx_loader import load_docx
from loaders.excel_loader import load_excel
from loaders.csv_loader import load_csv
from loaders.sqlite_loader import load_sqlite
from loaders.text_loader import load_text

def detect_file_type(file_path):
    """
    Detects the file type based on extension.
    Returns one of: pdf, docx, excel, csv, sqlite, text
    """
    ext = Path(file_path).suffix.lower()
    return {
        '.pdf': 'pdf',
        '.docx': 'docx',
        '.xlsx': 'excel',
        '.csv': 'csv',
        '.sqlite': 'sqlite',
        '.db': 'sqlite'
    }.get(ext, 'text')

def load_file(file_path):
    """
    Loads a file and returns its text content using the appropriate loader.
    """
    file_type = detect_file_type(file_path)
    loader_map = {
        'pdf': load_pdf,
        'docx': load_docx,
        'excel': load_excel,
        'csv': load_csv,
        'sqlite': load_sqlite,
        'text': load_text
    }
    return loader_map[file_type](file_path)


def chunk_text(text, chunk_size=500, chunk_overlap=50):
    """
    Splits text into chunks using RecursiveCharacterTextSplitter from langchain.
    """
    try:
        from langchain.text_splitter import RecursiveCharacterTextSplitter
    except ImportError:
        raise ImportError("langchain is required for chunking. Please install it.")
    splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
    return splitter.split_text(text)
